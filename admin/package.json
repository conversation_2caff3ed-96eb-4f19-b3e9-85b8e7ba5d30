{"name": "admin", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --configuration local --port 9016 --host 0.0.0.0", "build:dev": "ng build --configuration development", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.1", "@angular/cdk": "^18.2.14", "@angular/common": "^19.2.1", "@angular/compiler": "^19.2.1", "@angular/core": "^19.2.1", "@angular/forms": "^19.2.1", "@angular/platform-browser": "^19.2.1", "@angular/platform-browser-dynamic": "^19.2.1", "@angular/router": "^19.2.1", "@ckeditor/ckeditor5-angular": "^8.0.0", "@ng-select/ng-select": "^14.2.0", "@primeng/themes": "^19.0.9", "angular-ui-sortable": "^0.19.0", "angularx-flatpickr": "^8.1.0", "ckeditor5": "43.2.0", "file-upload-with-preview": "^6.1.2", "flatpickr": "^4.6.13", "moment": "^2.30.1", "ngx-cookie-service": "^18.0.0", "ngx-scrollbar": "^16.0.0-beta.2", "primeicons": "^7.0.0", "primeng": "^19.0.9", "rxjs": "~7.8.0", "slugify": "^1.6.6", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.1", "@angular/cli": "^19.2.1", "@angular/compiler-cli": "^19.2.1", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/typography": "^0.5.15", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.20", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "typescript": "~5.5.2"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}}
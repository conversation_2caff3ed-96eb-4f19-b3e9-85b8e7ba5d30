import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {TranslationService} from "@/services/translation.service";
import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component";
import { ToasterService } from "@/services/toaster.service";

@Component({
    selector: 'app-lang',
    imports: [RouterLink, AdminDialogComponent],
    templateUrl: './translations.component.html',
    styleUrl: './translations.component.scss'
})
export class TranslationsComponent {
  @ViewChild('dialog') dialog!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  message: string = '';
  translationService = inject(TranslationService)
  router = inject(Router)
  toasterService = inject(ToasterService)
  ngOnInit() {
    this.translationService.getTranslations().subscribe()
  }

  delete(code: string) {
    this.adminDialog.showConfirm('Вы уверены, что хотите удалить этот перевод?').then((confirmed) => {
      if (confirmed) {
        this.translationService.delete(code).subscribe(() => {
          this.toasterService.showSuccess('Перевод успешно удален');
        });
      }
    });
  }

  openModal(message: string) {
    this.message = message;
    this.dialog.nativeElement.showModal();
  }

  closeModal(dialog: HTMLDialogElement) {
    dialog.close();
  }
}

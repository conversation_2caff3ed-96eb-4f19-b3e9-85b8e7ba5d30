<div class="admin-component">
  <!-- Page Header -->
  <div class="admin-page-header">
    <div>
      <h1 class="admin-page-title">Переводы</h1>
    </div>
    <div class="admin-actions">
      <button type="button" class="btn btn-primary" [routerLink]="['/translations/add']">Добавить элемент</button>
    </div>
  </div>

  <!-- Admin Dialog Component -->
  <admin-dialog></admin-dialog>

  <!-- Modal -->
  <dialog #dialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="closeModal(dialog)" class="btn btn-primary">OK</button>
      </div>
    </div>
  </dialog>

  <!-- Content -->
  <div class="admin-content-wrapper">
    <div class="table-responsive">
      <table>
        <thead>
          <tr>
            <th>Код</th>
            <th>RU</th>
            <th>EN</th>
            <th>DE</th>
            <th>UA</th>
            <th>IT</th>
            <th class="text-center w-64">Действия</th>
          </tr>
        </thead>
        <tbody>
          @if(translationService.translations()) {
            @for(translation of translationService.translations(); track translation.code) {
              <tr>
                <td class="font-mono text-sm">{{translation.code}}</td>
                @for(lang of translation.translations; track lang) {
                  <td class="max-w-xs truncate" [title]="lang.text">{{lang.text}}</td>
                }
                <td>
                  <div class="admin-table-actions">
                    <button class="btn btn-sm btn-primary" (click)="router.navigate(['/translations/' + translation.id])">Редактировать</button>
                    <button class="btn btn-sm btn-danger" (click)="delete(translation.code)">Удалить</button>
                  </div>
                </td>
              </tr>
            }
          }
        </tbody>
      </table>
    </div>
  </div>
</div>


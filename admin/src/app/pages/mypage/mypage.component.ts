import {Component, ElementRef, inject, ViewChild} from '@angular/core';
import {Router, RouterLink} from "@angular/router";
import {MypageService} from "@/services/mypage.service";
import { AdminDialogComponent } from "@/components/admin-dialog/admin-dialog.component";
import { ToasterService } from "@/services/toaster.service";

@Component({
    selector: 'app-mypage',
    imports: [
        RouterLink,
        AdminDialogComponent
    ],
    templateUrl: './mypage.component.html',
    styleUrl: './mypage.component.scss'
})
export class MypageComponent {
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  @ViewChild('modal') modal!: ElementRef<HTMLDialogElement>;
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;
  message: string = "";
  mypageService = inject(MypageService);
  router = inject(Router)
  toasterService = inject(ToasterService);
  items: any = []

  ngOnInit(){
    this.getAll()
  }

  getAll() {
    this.mypageService.getAll().subscribe((res: any) => this.items = res)
  }
  
  openModal(message: string) {
    this.message = message;
    this.modal.nativeElement.showModal();
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    return this.adminDialog.showConfirm(message);
  }

  removePage(id: number) {
    this.openConfirmationDialog('Удалить страницу?').then((confirmed) => {
      if (confirmed) {
        this.mypageService.removePage(id).subscribe({
          next: () => {
            this.toasterService.showSuccess('Страница успешно удалена');
            this.getAll()
          },
          error: () => {
            this.toasterService.showError('Ошибка удаления, попробуйте еще раз');
            this.getAll()
          }
        });
      }
    });
  }
}

import { AdminDialogComponent } from '@/components/admin-dialog/admin-dialog.component';
import { FileService } from "@/services/file.service";
import { UserService } from "@/services/user.service";
import { CommonModule } from "@angular/common";
import { Component, inject, ViewChild } from '@angular/core';
import { FormBuilder, ReactiveFormsModule, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import moment from "moment";
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectModule } from 'primeng/select';
import { TableModule } from 'primeng/table';
import { environment } from "../../../../environments/environment";
import { countriesData } from './countries.var';
import { ToasterService } from "@/services/toaster.service";

@Component({
    selector: 'app-user',
    standalone: true,
    imports: [ReactiveFormsModule, CommonModule, SelectModule, MultiSelectModule, AdminDialogComponent, TableModule],
    templateUrl: './user.component.html',
    styleUrl: './user.component.scss'
})
export class UserComponent {
  @ViewChild(AdminDialogComponent) adminDialog!: AdminDialogComponent;

  fb = inject(FormBuilder);
  userService = inject(UserService);
  fileService = inject(FileService);
  route = inject(ActivatedRoute);
  router = inject(Router);
  toasterService = inject(ToasterService);
  user: any = this.fb.group({
    id: [null],
    firstName: [null],
    lastName: [null],
    middleName: [null],
    spiritualName: [null],
    email: [null, [Validators.required, Validators.email]],
    phone: [null],
    telegram: [null],
    avatar: [null],
    statuses: [[]],
    groups: [[]],
    birthDate: [null],
    country: [null],
    city: [null],
    address: [null],
    language: [null],
    dpDate: [null],
    dpLevel: [null],
    dpPractice: [null],
    dpEvents: [null],
    dpMeet: [null],
    dpGoal: [null],
    education: ['не выбрано'],
    health: [null],
    speciality: [null],
    profession: [null],
    skills: [null],
    service: [null],
    supportInstructor: [null],
    supportConsultation: [null],
    supportCorrespondence: [null],
    active: true,
    confirmed: false,
    comment: [null],
    lastLoginAt: null,
    lastActivity: null,
    subscriptions: [[]]
  })
  statuses: any = []
  groups: any = []
  tabs = ['Основная', 'Духовный путь', 'Образование', 'Здоровье', 'Обратная связь', 'Подписки']
  selectedTab = 0

  subscriptionColumns = [
    { field: 'createdAt', header: 'Дата' },
    { field: 'type', header: 'Тип' },
    { field: 'isAutoRenew', header: 'Автопродление' },
    { field: 'currentPeriodEnd', header: 'Действует до' },
    { field: 'provider', header: 'Платежная система' },
    { field: 'actions', header: 'Действия' }
  ]

  subscriptionTypes = this.userService.getSubscriptionTypes()
  showAddSubscription = false
  newSubscription = this.fb.group({
    type: ['', Validators.required],
    currentPeriodEnd: ['', Validators.required],
    isAutoRenew: [false]
  })

  ngOnInit() {
    this.userService
      .getOne(this.route.snapshot.params['id'])
      .subscribe((user: any) => this.user.patchValue(user));

    this.userService.getStatuses().subscribe(
      statuses => this.statuses = statuses
    );

    this.userService.getGroups().subscribe(
      (res: any) => this.groups = res
    )
  }

  get isAdmin(): boolean {
    return this.userService.hasGroup('ADMIN');
  }

  getGroupLabel(groupValue: string): string {
    return this.groups.find((g: any) => g.value === groupValue)?.label || groupValue;
  }

  get lastActivity() {
    return moment(this.user.value.lastActivity).format('YYYY-MM-DD HH:mm:ss');
  }

  onGroupChange(event: any) {
    if (event.value?.length > 1) {
      this.user.get('statuses')?.setValue([event.value.pop()]);
    }
  }

  uploadAvatar(e: Event) {
    const files = (e.target as HTMLInputElement).files!
    this.fileService.upload(files, 'avatar').subscribe((res: any) => this.user.value.avatar = res[0])
  }

  saveUser() {
    this.userService.updateUser(this.user.value).subscribe(() => {
      this.toasterService.showSuccess('Данные успешно обновлены');
    })
  }

  async deleteUser() {
    const confirmed = await this.adminDialog.showConfirm('Вы уверены, что хотите удалить этого пользователя?');
    if (!confirmed) return;

    this.userService.deleteUser(this.user.value.id).subscribe(() => {
      this.router.navigate(['users']);
    })
  }

  protected readonly environment = environment;

  // Array of countries
  countries = countriesData
  protected readonly moment = moment;

  toggleAddSubscription() {
    this.showAddSubscription = !this.showAddSubscription
    if (!this.showAddSubscription) {
      this.newSubscription.reset()
    }
  }

  addSubscription() {
    if (this.newSubscription.valid) {
      const data = this.newSubscription.value
      this.userService.addSubscription(this.user.value.id, data).subscribe(() => {
        this.toasterService.showSuccess('Подписка успешно добавлена');
        this.showAddSubscription = false
        this.newSubscription.reset()
        this.userService.getOne(this.user.value.id).subscribe((user: any) => {
          this.user.patchValue(user)
        })
      })
    }
  }

  async removeSubscription(subscriptionId: number) {
    const confirmed = await this.adminDialog.showConfirm('Вы уверены, что хотите удалить эту подписку?');
    if (!confirmed) return;

    this.userService.removeSubscription(subscriptionId).subscribe(() => {
      this.toasterService.showSuccess('Подписка успешно удалена');
      this.userService.getOne(this.user.value.id).subscribe((user: any) => {
        this.user.patchValue(user)
      })
    })
  }
}

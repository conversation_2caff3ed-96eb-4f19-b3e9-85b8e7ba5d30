import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component";
import { environment } from "@/env/environment";
import { ProfileService } from "@/services/profile.service";
import { ToasterService } from "@/services/toaster.service";
import { CommonModule, NgOptimizedImage } from "@angular/common";
import { Component, inject, ElementRef, ViewChild } from '@angular/core';
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

@Component({
  selector: 'app-subscribtions',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    FormsModule
  ],
  templateUrl: './subscribtions.component.html',
  styleUrl: './subscribtions.component.scss'
})
export class SubscriptionsComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  router = inject(Router);
  route = inject(ActivatedRoute);
  toasterService = inject(ToasterService);
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;
  message: string = "";
  subscriptions: any = {}

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['stripe', Validators.required],
    autoRenew: [true],
    isYearly: [false]
  })

  ngOnInit() {
    this.profileService.getProfile().subscribe()
    this.init();
  }

  init() {
    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res)
  }

  paySubscription() {
    this.profileService.paySubscription(this.subscriptionForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    })
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]))
  }

  cancelAutoRenew(sub: any) {
    this.openConfirmationDialog('Отменить подписку?').then((confirmed) => {
      if (confirmed) {
        this.profileService.cancelAutoRenew(sub.id).subscribe({
          next: () => {
            this.toasterService.showToast('Подписка отменена!', 'success', 'bottom-middle', 3000);
            this.profileService.getProfile().subscribe(p => {
              this.profileService.profile = p;
            });
          },
          error: () => {
            this.toasterService.showToast('Ошибка отмены подписки, попробуйте еще раз!', 'error', 'bottom-middle', 3000);
          }
        });
      }
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  protected readonly environment = environment;
}

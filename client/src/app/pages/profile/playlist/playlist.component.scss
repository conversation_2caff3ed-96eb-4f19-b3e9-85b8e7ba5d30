:host {
  dialog.stylized_wide {
    height: fit-content;
    padding: 54px 0;
  }

  .rename-input {
    background: transparent;
    border: 1px solid var(--text-color);
    border-radius: 8px;
    padding: 12px 16px;
    color: #000;
    font-family: inherit;
    font-size: 22px;
    font-weight: 400;
    outline: none;
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;

    &:focus {
      border-color: var(--pl_start1);
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
    }

    &::placeholder {
      color: var(--font-color1);
      opacity: 0.6;
    }
  }
}

.dialog-message {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  letter-spacing: 0;
  text-align: center;
  color: var(--font-color1);
}

.preloader {
  position: absolute;
  background-color: rgba(0, 0, 0, .1);
  border-radius: 21px;
}

.confirm-btn {
  width: 234px;
  height: 50px;
  padding: 0 !important;
  position: relative;
  background: transparent;

  &:focus {
    outline: none;
  }

  &.focus-visible {
    outline: none;
  }

  .btn-backdrop-img {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    object-fit: cover;
  }

  .confirm-btn-label {
    margin: 0 auto;
    font-family: Prata;
    font-weight: 400;
    font-size: 20px;
    line-height: 20px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    padding: 14px 25px;
    color: var(--font-color1);
  }
}

.playlist-wrapper {
  max-width: 930px;
  margin: 0 auto;
}

.playlist-item {
  padding: 26px 32px 30px 58px;
  border-top: 1px solid var(--book_about);

  &:last-of-type {
    border-bottom: 1px solid var(--book_about);
  }

  &:hover {
    background-color: var(--selection);
  }
}

.playlist-name {
  font-family: Prata;
  font-weight: 400;
  font-size: 24px;
  line-height: 24px;
  letter-spacing: 0;
  color: var(--font-color1);

}

.playlist-count {
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 18px;
  letter-spacing: 0;
  color: var(--text-color);
}

.play-all-btn,
.back-to-playlists-btn {
  border: 1px solid var(--text-color);
  border-radius: 10px;
  background-color: transparent;
  cursor: pointer;
  color: var(--font-color1);
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 24px;
  letter-spacing: 0;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 20px;
}

.track-item {
  padding: 20px 33px 20px 20px;
  border-top: 1px solid var(--text-color);

  &:last-of-type {
    border-bottom: 1px solid var(--text-color);
  }
}

.track-name {
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0;
  color: var(--font-color1);
}

.track-bottom-info {
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 26px;
  letter-spacing: 0;
  color: var(--text-color);
}


.on_hov {
  display: none;
  position: absolute;
  font-family: Prata;
  font-weight: 400;
  font-size: 15px;
  line-height: 20px;
  height: 50px;
  padding: 0 16px;
  background-color: var(--selection);
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  color: var(--font-color);
  white-space: nowrap;
  top: 33px;
  z-index: 5;
}

.on_hov::before {
  content: '';
  background-image: var(--triangl_w);
  display: block;
  background-repeat: no-repeat;
  background-position: center;
  width: 48px;
  height: 17px;
  position: absolute;
  top: -10px;
}

.lik_hov:hover {
  .on_hov {
    display: flex;
  }
}

.shr_hov:hover {
  .on_hov {
    display: flex;
  }
}

.fav_hov:hover {
  .on_hov {
    display: flex;
  }
}

.lik_hov {
  position: relative;

  .on_hov {
    left: -85px;
  }

  .on_hov::before {
    right: 14px;
  }
}

.shr_hov {
  position: relative;

  .on_hov {
    left: -47px;
  }
}

.fav_hov {
  position: relative;

  .on_hov {
    left: -30px;
  }

  .on_hov::before {
    left: 19px;
  }
}

.desktop-actions {
  display: flex;
}

.mobile-actions {
  display: none;
}

.dropdown-content-wrapper {
  padding-top: 18px;
  width: max-content;
  right: 7px;
  top: 0;
  position: absolute;
  z-index: 10;
}

.dropdown-content {
  /* display: none; */
  border: 0.1rem solid transparent;
  border-radius: 10px 0 10px 10px;
  background: linear-gradient(to right, #fff, #fff),
    linear-gradient(to right, #D19036, #D19036);
  background-clip: padding-box,
    border-box;
  background-origin: padding-box,
    border-box;
  padding: 15px 0;
  position: relative;
  // &::before {
  //   content: '';
  //   background-image: url(assets/images/icons/tri__.svg);
  //   background-repeat: no-repeat;
  //   background-position: center;
  //   background-size: contain;
  //   display: flex;
  //   width: 22px;
  //   height: 18px;
  //   position: absolute;
  //   right: -0.12rem;
  //   top: -0.9975rem;
  // }
}

.dropdown-item {
  padding: 2px 10px;
  font-family: Prata;
  font-weight: 400;
  font-size: 14px;
  line-height: 15px;
  color: var(--font-color1);
  cursor: pointer;
}

.dropdown-item:hover {
  background: var(--selection);
}

@media (max-width: 1250px) {
  .playlist-wrapper {
    max-width: 768px;
  }

  .playlist-item {
    padding: 21px 0;
  }

  .playlist-name {
    font-size: 18px;
    line-height: 22px;

  }

  .playlist-count {
    font-size: 15px;
    line-height: 18px;
  }

  .track-item {
    padding: 24px 0;

    .play-icon {
      width: 52px;
      height: 52px;
    }
  }

  .track-bottom-info {
    flex-direction: column;
    gap: 10px;
    font-size: 15px;
    line-height: 16px;

    .track-time-info {
      flex-direction: row-reverse;
      justify-content: flex-end;
      gap: 15px;

      svg {
        width: 19px;
        height: 19px;
      }
    }
  }

  .selected-playlist {
    align-items: flex-start;

    .playlist-count {
      font-size: 17px;
      line-height: 17px;
    }

  }

  .track-main-info {
    gap: 22px;
  }

  .playlist-head-actions {
    flex-direction: column;
  }

  .back-to-playlists-btn {
    font-size: 15px;
    line-height: 16px;
  }

  .play-all-btn {
    font-size: 15px;
    line-height: 16px;
  }

  .drag-btn {
    padding-top: 17px;
  }
}

@media (max-width: 768px) {
  .desktop-actions {
    display: none;
  }

  dialog.stylized_wide {
    height: fit-content !important;
    padding: 40px 10px 20px !important;
  }

  .confirm-btn {
    zoom: 0.9;
  }


  .playlist-wrapper {
    max-width: 580px;
  }

  .mobile-actions {
    display: block;
    cursor: pointer;
    width: 20px;
    display: flex;
    justify-content: flex-end;
    height: 25px;
    position: relative;
    margin-top: 14px;
  }
}

@media (max-width: 720px) {
  .playlist-wrapper {
    max-width: 460px;
    padding: 0 8px;
  }

  .playlist-item {
    padding: 16px 0 20px;
  }

  .playlist-name {
    font-size: 15px;
    line-height: 16px;

  }

  .playlist-count {
    font-size: 15px;
    line-height: 16px;
  }

  .track-item {
    padding: 16px 0 26;

    .drag-btn {
      svg {
        width: 16px;
        height: 16px;
      }
    }

    .play-icon {
      width: 44px;
      height: 44px;
    }
  }

  .selected-playlist {
    flex-direction: column;
    gap: 40px;
  }

  .track-name {
    font-size: 15px;
    line-height: 17px;
  }

  .track-bottom-info {
    gap: 6px;
    font-size: 14px;
    line-height: 13px;

    .track-time-info {
      div {
        gap: 5px;
      }

      svg {
        width: 15px;
        height: 15px;
      }
    }
  }

  .track-main-info {
    gap: 10px;
  }

  .track-left-icons {
    gap: 10px;
  }

  .playlist-head-actions {
    gap: 15px;
    width: 100%;
  }

  .back-to-playlists-btn {
    width: 100%;
    font-size: 17px;
    line-height: 17px;
    padding: 10px 22px;
    position: relative;

    .btn-label {
      position: absolute;
      white-space: nowrap;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .play-all-btn {
    width: 100%;
    font-size: 17px;
    line-height: 17px;
    padding: 10px 22px;
    position: relative;

    .btn-label {
      position: absolute;
      white-space: nowrap;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .drag-btn {
    padding-top: 14px;
  }
}

@media (max-width: 570px) {
  .dropdown-content-wrapper {
    right: 11px;
    top: -7px;
  }
}

@media (max-width: 550px) {
  .dialog-message {
    font-size: 20px;
  }

  .dialog-footer {
    flex-direction: column;
    align-items: center;
    gap: 16px;

    .confirm-btn {
      zoom: 0.8;
    }
  }
}
import { BreadcrumbComponent } from "@/components/breadcrumb/breadcrumb.component"
import { environment } from "@/env/environment"
import { ProfileService } from "@/services/profile.service"
import { ToasterService } from "@/services/toaster.service"
import { CommonModule, NgOptimizedImage } from "@angular/common"
import { Component, ElementRef, inject, ViewChild } from '@angular/core'
import { FormBuilder, FormsModule, ReactiveFormsModule, Validators } from "@angular/forms"
import { ActivatedRoute, NavigationEnd, Router } from "@angular/router"
import moment from "moment/moment"
import { filter } from "rxjs/operators"
import { FavouritesComponent } from "./favourites/favourites.component"
import { MyDataComponent } from "./my-data/my-data.component"
import { PlaylistComponent } from "./playlist/playlist.component"

enum ProfileTabs {
  FAVORITES = 'Избранное',
  PLAYLISTS = 'Плейлисты',
  MY_DATA = 'Мои данные',
  SUBSCRIPTIONS = 'Подписки',
}

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    CommonModule,
    BreadcrumbComponent,
    NgOptimizedImage,
    FavouritesComponent,
    PlaylistComponent,
    MyDataComponent,
    FormsModule
  ],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent {
  profileService = inject(ProfileService)
  fb = inject(FormBuilder)
  router = inject(Router);
  route = inject(ActivatedRoute);
  toasterService = inject(ToasterService);
  profileTabs: ProfileTabs[] = Object.values(ProfileTabs);
  activeTab: ProfileTabs = this.profileTabs[0];
  ProfileTabs = ProfileTabs;
  subscriptions: any = {}
  message: string = "";

  subscriptionForm = this.fb.group({
    type: [null, Validators.required],
    payment: ['stripe', Validators.required],
    autoRenew: [true],
    isYearly: [false]
  })

  // Маппинг табов к URL путям
  private tabToRouteMap: Record<ProfileTabs, string> = {
    [ProfileTabs.FAVORITES]: 'favorites',
    [ProfileTabs.MY_DATA]: 'my-data',
    [ProfileTabs.PLAYLISTS]: 'playlists',
    [ProfileTabs.SUBSCRIPTIONS]: 'subscriptions',
  };

  @ViewChild('tabs_w') tabs_w!: ElementRef;
  @ViewChild('confirmDialog') confirmDialog!: ElementRef<HTMLDialogElement>;

  ngOnInit() {
    this.profileService.getProfile().subscribe()

    this.init();

    // Подписываемся на изменения роутера для определения активного таба
    this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        const url = this.router.url;
        if (url.includes('/profile/favorites')) {
          this.activeTab = ProfileTabs.FAVORITES;
        } else if (url.includes('/profile/my-data')) {
          this.activeTab = ProfileTabs.MY_DATA;
        } else if (url.includes('/profile/playlists')) {
          this.activeTab = ProfileTabs.PLAYLISTS;
        } else if (url.includes('/profile/subscriptions')) {
          this.activeTab = ProfileTabs.SUBSCRIPTIONS;
        }

        this.scrollToActiveTab();
      });

    // Инициализируем активный таб при загрузке
    const currentUrl = this.router.url;
    if (currentUrl.includes('/profile/favorites')) {
      this.activeTab = ProfileTabs.FAVORITES;
    } else if (currentUrl.includes('/profile/my-data')) {
      this.activeTab = ProfileTabs.MY_DATA;
    } else if (currentUrl.includes('/profile/playlists')) {
      this.activeTab = ProfileTabs.PLAYLISTS;
    } else if (currentUrl.includes('/profile/subscriptions')) {
      this.activeTab = ProfileTabs.SUBSCRIPTIONS;
    }
  }

  init() {
    this.profileService.getSubscriptions().subscribe(res => this.subscriptions = res)
  }

  selectTab(tab: ProfileTabs): void {
    this.navigateToTab(tab);
  }

  private navigateToTab(tab: ProfileTabs): void {
    const route = this.tabToRouteMap[tab];
    this.router.navigate(['/ru/profile', route]);
  }

  private scrollToActiveTab(): void {
    setTimeout(() => {
      if (this.tabs_w?.nativeElement) {
        const tabsContainer = this.tabs_w.nativeElement;
        const activeTabElement = tabsContainer.querySelector('.is-active');

        if (activeTabElement) {
          const containerWidth = tabsContainer.offsetWidth;
          const tabWidth = activeTabElement.offsetWidth;
          const tabLeft = activeTabElement.offsetLeft;

          const scrollPosition = tabLeft - (containerWidth / 2) + (tabWidth / 2);

          tabsContainer.scrollTo({
            left: scrollPosition,
            behavior: 'smooth'
          });
        }
      }
    }, 0);
  }



  paySubscription() {
    this.profileService.paySubscription(this.subscriptionForm.value).subscribe((res: any) => {
      location.href = res.paymentUrl;
    })
  }

  activeUntil(item: any) {
    return moment(item.createdAt).add(item.isYearly ? 12 : 1, 'months').format('DD.MM.YYYY');
  }

  get subscriptionsFiltered() {
    return Object.fromEntries(Object.keys(this.subscriptions)
      .filter(key => !this.profileService.profile?.subscriptions.some((e: any) => e.type === key))
      .map(key => [key, this.subscriptions[key]]))
  }

  cancelAutoRenew(sub: any) {
    this.openConfirmationDialog('Отменить подписку?').then((confirmed) => {
      if (confirmed) {
        this.profileService.cancelAutoRenew(sub.id).subscribe({
          next: () => {
            this.toasterService.showToast('Подписка отменена!', 'success', 'bottom-middle', 3000);
            this.profileService.getProfile().subscribe(p => {
              this.profileService.profile = p;
            });
          },
          error: () => {
            this.toasterService.showToast('Ошибка отмены подписки, попробуйте еще раз!', 'error', 'bottom-middle', 3000);
          }
        });
      }
    });
  }

  closeModal(modal: HTMLDialogElement) {
    modal.close();
  }

  openConfirmationDialog(message: string): Promise<boolean> {
    this.message = message;
    this.confirmDialog.nativeElement.showModal();
    return new Promise((resolve) => {
      const okButton = this.confirmDialog.nativeElement.querySelector('.ok-button');
      const cancelButton = this.confirmDialog.nativeElement.querySelector('.cancel-button');
      const closeDialog = (confirmed: boolean) => {
        this.closeModal(this.confirmDialog.nativeElement);
        resolve(confirmed);
      };
      okButton?.addEventListener('click', () => closeDialog(true), { once: true });
      cancelButton?.addEventListener('click', () => closeDialog(false), { once: true });
    });
  }

  protected readonly environment = environment;
}

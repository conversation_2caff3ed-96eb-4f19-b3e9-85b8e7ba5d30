@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';

header {
    height: 108px;
    position: fixed;
    top: 0;
    width: 100%;
    background-color: transparent;
    padding: 16px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
    z-index: 999;

    svg {
        outline: none;
        &:focus {
            outline: none;
        }
    }

    &.scrolled {
        background-color: main(50);
        // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
        transition: background-color 0.25s ease-in-out;
    }

    &.link-menu-opened {
        background-color: main(50);
    }

    &.burger-menu-opened {
        background-color: main(50);
        border-bottom: 1px solid main(300);
        z-index: 10010;
    }

    .logo {
        width: 62px;
        height: 62px;
        cursor: pointer;
        position: absolute;
        top: 9px;
        left: 50%;
        transform: translateX(-50%);
        display: none;
    }

    .burger-menu-title {
        @include subtitle-1;
        color: main(600);
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }

    .left-actions {
        display: flex;
        align-items: center;
        gap: 24px;

        .burger-menu-toggle {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .burger-icon {
                width: 16px;
                height: 14px;
                position: relative;
                transition: 0.5s ease-in-out;

                span {
                    display: block;
                    position: absolute;
                    height: 2px;
                    width: 100%;
                    background: main(700);
                    border-radius: 2px;
                    left: 0;
                    transition: 0.25s ease-in-out;

                    &:nth-child(1) {
                        top: 0;
                    }

                    &:nth-child(2) {
                        top: 6px;
                    }

                    &:nth-child(3) {
                        top: 12px;
                    }
                }

                &.active span {
                    &:nth-child(1) {
                        top: 6px;
                        transform: rotate(135deg);
                    }

                    &:nth-child(2) {
                        opacity: 0;
                        left: -60px;
                    }

                    &:nth-child(3) {
                        top: 6px;
                        transform: rotate(-135deg);
                    }
                }
            }
        }
    }

    .right-actions {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
    }

    a {
        position: relative;
        color: main(700);
        text-decoration: none;
        cursor: pointer;
        &.active {
            &::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: main(600);
        }
        }

        &::after {
            content: '';
            position: absolute;
            bottom: -4px;
            right: 0;
            width: 0;
            height: 2px;
            background-color: main(600);
            transition: width 0.2s ease-in-out;
        }

        &:hover::after {
            width: 100%;
            left: 0;
            right: auto;
        }
    }

}

.link-menu {
    height: fit-content;
    max-height: 0px;
    position: fixed;
    top: 108px;
    width: 100%;
    background-color: main(50);
    padding: 48px 15px 40px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    box-shadow: 0px 1px 5.8px 0px #42381E33;
    opacity: 0;
    transform: translateY(-20px);
    transition: all 0.3s ease-in-out;
    z-index: 998;

    &.opened {
        opacity: 1;
        transform: translateY(0);
        min-height: 282px;
        max-height: 100%;
    }

    .link-menu-item {
        display: flex;
        gap: 20px;
        flex-basis: calc(25% - 23px);
        max-width: calc(25% - 23px);

        .item-img {
            width: 90px;
            height: 90px;
            background-size: cover;
            position: relative;

            .img-mask {
                background: url(../../../assets/images/main-v2/head-menu-mask.webp) no-repeat center;
                background-size: contain;
                width: 90px;
                height: 90px;
                position: sticky;
                z-index: 2;
            }
        }

        .link-menu-item-text-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 16px;

            .title {
                @include body-1;
                color: main(700);
            }

            .description {
                @include body-2;
                color: main(500);

            }
        }
    }

    .show-more {
        cursor: pointer;
        @include button-1;
        color: main(700);
    }
}

.desktop-burger-menu {
    height: calc(100% - 108px);
    position: fixed;
    top: 108px;
    right: 0;
    width: 100%;
    z-index: 10010;
    background: main(50);
    display: flex;

    .left-link-bar {
        width: 225px;
        min-width: 225px;
        padding: 24px 15px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 20px;
        border-right: 1px solid main(300);

        .link-bar-item {
            display: flex;
            align-items: center;
            cursor: pointer;
            gap: 12px;
            padding: 20px 0;
            @include button-1;
            color: main(500);
            transition: color 0.2s ease-in-out;

            &:hover {
                color: main(600);
            }

            &.active {
                color: main(900);
                white-space: nowrap;
            }

            img {
                width: 19px;
                height: 19px;
                margin-bottom: 3px;
            }
        }
    }

    .burger-menu-main-content {
        display: flex;
        flex-direction: column;
        gap: 80px;
        padding: 48px 15px 48px 30px;
        overflow-y: auto;
        width: 100%;

        section {
            display: flex;
            flex-direction: column;
            gap: 24px;
            padding-bottom: 40px;
            border-bottom: 1px solid #DA9C504D;

            &:last-of-type {
                border-bottom: none;
            }

            .section-title {
                @include caption-2;
                color: main(600);
                opacity: 60%;

                img {
                    display: none;
                }
            }

            .links-cols {
                display: flex;
                gap: 30px;
                width: 930px;
                flex-wrap: wrap;

                &:has(.buttons-col) {
                    width: 100%;
                }

                .links-col {
                    display: flex;
                    flex-direction: column;
                    gap: 24px;
                    width: 25%;
                    max-width: 210px;

                    &.buttons-col {
                        flex: 1 1 0;
                        max-width: none;
                        align-items: flex-end;

                        .col-name {
                            width: 210px;
                        }
                    }

                    .col-name {
                        svg {
                            display: none;
                        }

                        @include body-2;
                        color: main(500);
                    }

                    .links-wrapper {
                        display: flex;
                        flex-direction: column;
                        gap: 24px;

                        a {
                            @include button-2;
                            color: main(800);
                            text-decoration: none;
                            cursor: pointer;
                            position: relative;
                            width: fit-content;

                            &::after {
                                content: '';
                                position: absolute;
                                bottom: -4px;
                                right: 0;
                                width: 0;
                                height: 2px;
                                background-color: main(600);
                                transition: width 0.2s ease-in-out;
                            }

                            &:hover::after {
                                width: 100%;
                                left: 0;
                                right: auto;
                            }
                        }

                        button {
                            @include button-4;
                            color: main(600);
                            background: url(../../../assets/images/main-v2/stroke-button-big.webp) no-repeat center;
                            background-size: contain;
                            border: none;
                            cursor: pointer;
                            width: fit-content;
                            width: 216px;
                            height: 52px;
                            transition: all 0.2s ease-in-out;

                            &:hover {
                                background: url(../../../assets/images/main-v2/stroke-button-big-hover.webp) no-repeat center;
                                background-size: contain;
                            }

                            &:active {
                                color: main(800);
                                background: url(../../../assets/images/main-v2/stroke-button-big-active.webp) no-repeat center;
                                background-size: contain;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 1200px) {
    .desktop-burger-menu {
        .left-link-bar {
            width: 194px;
            min-width: 194px;

            .link-bar-item {
                gap: 10px;
                padding: 16px 0;

                img {
                    width: 19px;
                    height: 19px;
                    margin-bottom: 3px;
                }
            }
        }

        .burger-menu-main-content {
            display: flex;
            flex-direction: column;
            gap: 48px;
            padding: 48px 20px;
            overflow-y: auto;
            width: 100%;

            section {

                .section-title {
                    @include caption-3;
                }

                .links-cols {
                    display: flex;
                    gap: 20px;
                    row-gap: 48px;
                    width: 100%;
                    flex-wrap: wrap;

                    .links-col {
                        gap: 16px;
                        width: 100%;
                        max-width: 160px;

                        &.buttons-col {
                            flex: 1 1 0;
                            max-width: none;
                            min-width: 100%;
                            align-items: flex-start;

                            .col-name {
                                width: 100%;
                                min-width: 100%;
                            }

                            .links-wrapper {
                                flex-direction: row;
                                flex-wrap: wrap;
                            }
                        }

                        .col-name {
                            @include caption-2;
                        }

                        .links-wrapper {
                            gap: 20px;

                            a {
                                @include button-4;
                            }

                            button {
                                background: url(../../../assets/images/main-v2/stroke-button-small.webp) no-repeat center;
                                background-size: contain;
                                width: 157px;
                                height: 52px;

                                span {
                                    width: 130px;
                                    display: block;
                                    margin: auto;
                                }

                                &:hover {
                                    background: url(../../../assets/images/main-v2/stroke-button-small-hover.webp) no-repeat center;
                                    background-size: contain;
                                }

                                &:active {
                                    background: url(../../../assets/images/main-v2/stroke-button-small-active.webp) no-repeat center;
                                    background-size: contain;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}

@media (max-width: 1100px) {
    header {
        height: 80px;

        .left-actions {
            a {
                display: none;
            }
        }

        .right-actions {
            a {
                display: none;
                &:last-of-type {
                    display: block;
                }
            }
        }

        .logo {
            display: block;
        }
    }

    .link-menu {
        display: none;
    }

    .desktop-burger-menu {
        height: calc(100% - 80px);
        top: 80px;
    }
}

@media (max-width: 600px) {
    header {
        .burger-menu-title {
            display: none;
        }
    }

    .desktop-burger-menu {
        .left-link-bar {
            display: none;
        }

        .burger-menu-main-content {
            display: flex;
            flex-direction: column;
            gap: 22px;
            padding: 30px 20px 0px;
            overflow-y: auto;
            width: 100%;

            section {
                padding-bottom: 0px;
                gap: 22px;
                border-color: main(300);

                .links-cols {
                    max-height: 0;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    gap: 30px;
                    width: 100%;
                    flex-wrap: nowrap;
                    transition: max-height 0.2s ease-in-out;
                }

                &.active {
                    .links-cols {
                        max-height: 1400px;
                    }
                    .section-title {
                        color: main(900);
                    }
                }

                .section-title {
                    cursor: pointer;
                    @include button-2;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    opacity: 100%;
                    color: main(500);
                    transition: all 0.2s ease-in-out;

                    img {
                        width: 18px;
                        height: 17px;
                        display: block;
                        margin-bottom: 4px;
                    }
                }

                .links-cols {
                    display: flex;
                    flex-direction: column;
                    row-gap: 12px;
                    padding-left: 22px;

                    .links-col {
                        gap: 16px;
                        width: 100%;
                        max-width: none;

                        &.active-col {
                            .col-name {
                                svg {
                                    transform: rotate(180deg);
                                }
                            }

                            .links-wrapper {
                                transition: max-height 0.2s ease-in-out;
                                max-height: 600px;
                                padding-bottom: 12px;
                            }
                        }

                        &.buttons-col {
                            flex: 1 1 0;
                            max-width: none;
                            min-width: 100%;
                            align-items: flex-start;

                            .col-name {
                                width: 100%;
                                min-width: 100%;
                            }

                            .links-wrapper {
                                flex-direction: column;
                                flex-wrap: wrap;
                            }
                        }

                        .col-name {
                            cursor: pointer;
                            @include button-3;
                            color: main(600);
                            display: flex;
                            align-items: center;
                            gap: 8px;

                            svg {
                                display: block;
                                transition: transform 0.2s ease-in-out;
                            }
                        }

                        .links-wrapper {
                            max-height: 0;
                            padding-left: 22px;
                            overflow: hidden;

                            a {
                                @include button-3;
                                color: main(500);
                            }

                            button {
                                background: none;
                                width: 100%;
                                height: auto;
                                text-align: start;

                                &::after {
                                    content: '';
                                    position: absolute;
                                    bottom: -4px;
                                    right: 0;
                                    width: 0;
                                    height: 2px;
                                    background-color: main(600);
                                    transition: width 0.2s ease-in-out;
                                }

                                &:hover::after {
                                    width: 100%;
                                    left: 0;
                                    right: auto;
                                }

                                span {
                                    width: auto;
                                    display: block;
                                    margin: 0;
                                }

                                &:hover {
                                    background: none;
                                }

                                &:active {
                                    background: none;
                                }
                            }
                        }
                    }
                }
            }
            .menu-mobile-footer {
                background: main(50);
                width: 100%;
                // height: 56px;
                // position: sticky;
                // bottom: 0;
                display: flex;
                justify-content: space-between;
                padding: 30px 16px;
                align-items: center;
                svg {
                    cursor: pointer;
                    &:hover {
                        path {
                            fill: main(600);
                        }
                    }
                }
            }
        }
    }

}
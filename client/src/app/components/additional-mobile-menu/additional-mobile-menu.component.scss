.add-menu_wrap {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 99px;
    background: var(--main-background-color1);
}

.center_menu {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--m-menu);
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    width: 70px;
    height: 70px;
    position: absolute;
    left: 50%;
    right: 50%;
    transform: translate(-50%, 0);
    top: -21px;
    cursor: pointer;

    div {
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--m-menu-burg);
        width: 26px;
        height: 26px;
    }

    // span {
    //     font-family: Prata;
    //     font-weight: 400;
    //     font-size: 14px;
    //     line-height: 14px;
    //     letter-spacing: 1px;
    //     color: var(--menu_font2);
    //     margin-top: 27px;
    // }
}

.side_wrap {
    display: flex;
    justify-content: space-around;
    margin-right: 30px;

    &.last_one {
        margin-right: 0;
        margin-left: 30px;
    }
}

.side_item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 22vw;
    margin-bottom: 10px;

    &.fst_ {
        .img_item {
            background: var(--m-books);
            width: 26px;
            height: 26px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
        }
    }

    &.scd_ {
        .img_item {
            background: var(--m-prs);
            width: 26px;
            height: 26px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
        }
    }

    &.trd_ {
        .img_item {
            background: var(--m-srch);
            width: 26px;
            height: 26px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
        }
    }

    &.lst_ {
        .img_item {
            background: var(--m-pl);
            width: 26px;
            height: 26px;
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
        }
    }

    .img_item {
        margin-bottom: 6px;
        cursor: pointer;
    }
}

.side_item {
    span {
        font-family: Prata;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        color: var(--font-color1);
    }
}
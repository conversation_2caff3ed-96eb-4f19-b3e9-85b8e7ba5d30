@import '../../../assets/styles/new-palette';
@import '../../../assets/styles/new-typography';


.sidebar-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  z-index: 10000;
  backdrop-filter: blur(30px);
  background: #190E0180;
}

.daily-sidebar-container {
  position: fixed;
  top: 0;
  right: 0;
  width: 674px;
  height: 100%;
  display: flex;
  transform: translateX(592px);
  transition: transform 0.4s ease-in-out;
  z-index: 997;
  &.daily-sidebar-is-open {
    z-index: 10001;
    transform: translateX(0);

    .marker-section {
      .marker {
        animation: rotateClockwise 8s linear infinite;
        animation-duration: 8s;
        transition: animation-duration 0.3s ease-in-out;

        &:hover {
          animation: rotateClockwise 3s linear infinite;
        }
      }
    }
  }

  .marker-section {
    display: flex;
    align-items: center;
    justify-content: center;

    .marker {
      width: 33px;
      height: 33px;
      border-radius: 20%;
      background: url(../../../assets/images/main-v2/wheel.webp) no-repeat center;
      background-size: contain;
      animation: rotateCounterClockwise 8s linear infinite;
      animation-duration: 8s;
      transition: animation-duration 0.3s ease-in-out;
      &.dark-wheel {
        background: url(../../../assets/images/main-v2/wheel-dark.webp) no-repeat center;
      }

      &:hover {
        animation: rotateCounterClockwise 3s linear infinite;
      }
    }
  }

  .main-sidebar-section {
    background: url(../../../assets/images/main-v2/BG-open.webp) no-repeat left center;
    background-size: cover;
    width: 641px;
    padding: 30px 62px 35px 112px;
    overflow-y: auto;

    .side-bar-header {
      @include h3;
      color: main(900);
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 40px;
    }

    .daily-name {
      @include subtitle-1;
      color: main(500);
      margin-bottom: 32px;

      @media (max-width: 680px) {
        @include subtitle-3;
        margin-bottom: 16px;
      }
    }

    .daily-description {
      @include body-1;
      color: main(600);
      margin-bottom: 48px;

      @media (max-width: 680px) {
        @include body-3;
        margin-bottom: 40px;
      }
    }

    .mantra-title {
      @extend .daily-name;
    }

    .mantra-name {
      @extend .daily-description;
    }

    .upcoming-events-box {
      margin-top: 48px;

      &_title {
        @include subtitle-1;
        color: main(500);
        margin-bottom: 32px;
      }

      .schedule-section {
        display: flex;
        flex-direction: column;
        position: relative;
        gap: 32px;

        .schedule-point-box {
          position: sticky;
          z-index: 2;
          display: flex;
          justify-content: space-between;
          gap: 16px;

          .day-box {
            text-align: center;
            display: flex;
            justify-content: center;
            height: inherit;
            width: 78px;
            
            &:has(.today) {
              @include body-1;
              color: main(700);
              align-items: start;
            }
            
            .date {
              &.default-m {
                margin-top: 20px;
              }
              background: #ffe8b4;
              height: 70px;
              padding: 10px 0;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              @include body-2;
              color: main(500);

              &.today {
                justify-content: center;
              }
            }

            .month {
              @include body-2;
              color: main(500);
            }
          }

          .schedule-point-info-cards {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .info-card {
              display: flex;
              gap: 10px;

              .img-section {
                width: 94px;
                height: 108px;
                background-size: cover;
                border-radius: 6px;

                &_mask {
                  background: url(../../../assets/images/main-v2/daily-img-mask.webp) no-repeat center;
                  background-size: contain;
                  width: 100%;
                  height: 24px;
                  position: sticky;
                  z-index: 2;
                  // margin-top: -1px;
                }
              }

              .info-box {
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                gap: 16px;
                width: 228px;

                .title {
                  @include body-2;
                  color: main(600);
                  cursor: pointer;
                }

                .tag {
                  @include caption-1;
                  color: main(500);
                  border: 1px solid main(300);
                  border-radius: 6px;
                  padding: 8px;
                  width: fit-content;
                }
              }
            }
          }
        }

        .load-more {
          text-align: center;
          cursor: pointer;
          @include button-4;
          color: main(700);
        }

        .schedule-dash-marker {
          height: calc(100% - 100px);
          width: 1px;
          background-image: linear-gradient(to bottom,
              main(300) 0%,
              main(300) 50%,
              transparent 50%,
              transparent 100%);
          background-size: 1px 16px;
          background-repeat: repeat-y;
          position: absolute;
          z-index: 1;
          margin-left: 36px;
          top: 24px;
        }
      }
    }
  }
}

@media (max-width: 680px) {
  .daily-sidebar-container {
    width: 100%;

    .marker-section {
      display: none !important;
    }

    .main-sidebar-section {
      background: linear-gradient(90deg, #FFE6AE 0%, #FFEBBF 25%, #FFEBBF 50.96%, #FFEBBF 75%, #FFE6AE 100%);
      width: 100%;
      padding: 30px 16px;

      .side-bar-header {
        @include subtitle-2;
      }

      .upcoming-events-box {
        margin-top: 48px;

        &_title {
          @include subtitle-1;
          color: main(500);
          margin-bottom: 32px;
        }

        .schedule-section {
          gap: 24px;

          .schedule-point-box {
            .day-box {
              width: 58px;
              &:has(.today) {
                @include body-3;
              }

              .date {
                @include button-5;
                height: 60px;
                &.default-m {
                  margin-top: 10px;
                }
              }

              .month {
                @include button-5;
              }
            }

            .schedule-point-info-cards {
              gap: 8px;

              .info-card {
                display: flex;
                gap: 8px;

                .img-section {
                  width: 80px;
                  height: 70px;
                  border-radius: 3px;

                  &_mask {
                    width: 100%;
                    height: 20px;
                  }
                }

                .info-box {
                  width: 300px;

                  .title {
                    @include button-5;
                  }

                  .tag {
                    @include caption-4;
                    border-radius: 4px;
                    padding: 6px;
                  }
                }
              }
            }
          }

          .schedule-dash-marker {
            height: calc(100% - 76px);
            width: 1px;
            background-image: linear-gradient(to bottom,
                main(300) 0%,
                main(300) 50%,
                transparent 50%,
                transparent 100%);
            background-size: 1px 16px;
            background-repeat: repeat-y;
            position: absolute;
            z-index: 1;
            margin-left: 28px;
            top: 24px;
          }
        }
      }
    }
  }
}

@media (max-width: 520px) {
  .daily-sidebar-container {
    .main-sidebar-section {
      .upcoming-events-box {
        .schedule-section {
          .schedule-point-box {
            .schedule-point-info-cards {
              .info-card {
                .info-box {
                  width: 168px;
                }
              }
            }
          }
        }
      }

    }
  }
}

@keyframes rotateCounterClockwise {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(-360deg);
  }
}

@keyframes rotateClockwise {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}
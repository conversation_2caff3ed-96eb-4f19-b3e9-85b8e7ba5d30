.btn-favourite svg {
  width: 20px;
  height: 20px;
}

.btn-favourite.in-favourites svg {
  fill: #ffc94a;
  stroke: none;
}

.btn-like svg {
  width: 24px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0 10px;
  font-family: Prata;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.5;
  color: var(--font-color1);
  height: 40px;
  cursor: pointer;

  span {
    margin-right: 12px;
  }
}

.addtnl_sp {
  position: absolute;
  width: 100%;
  height: 25px;
  top: -25px;
}

.add-menu_wrap {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 99px;
  background: var(--main-background-color1);
}

.center_menu {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--m-menu);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  width: 70px;
  height: 70px;
  position: absolute;
  left: 50%;
  right: 50%;
  transform: translate(-50%, 0);
  top: -21px;
  cursor: pointer;

  div {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--m-menu-burg);
    width: 26px;
    height: 26px;
  }

  // span {
  //   font-family: Prata;
  //   font-weight: 400;
  //   font-size: 14px;
  //   line-height: 14px;
  //   letter-spacing: 1px;
  //   color: var(--menu_font2);
  //   margin-top: 27px;
  // }
}

.side_wrap {
  display: flex;
  justify-content: space-around;
  margin-right: 30px;

  &.last_one {
    margin-right: 0;
    margin-left: 30px;
  }
}

.side_item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 22vw;
  margin-bottom: 10px;

  &.fst_ {
    .img_item {
      background: var(--m-books);
      width: 26px;
      height: 26px;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  &.scd_ {
    .img_item {
      background: var(--m-prs);
      width: 26px;
      height: 26px;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  &.trd_ {
    .img_item {
      background: var(--m-srch);
      width: 26px;
      height: 26px;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  &.lst_ {
    .img_item {
      background: var(--m-pl);
      width: 26px;
      height: 26px;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  &.lst_pl {
    .img_item {
      background: var(--m-pl-swch);
      width: 26px;
      height: 26px;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
    }
  }

  .img_item {
    margin-bottom: 6px;
    cursor: pointer;
  }
}

.side_item {
  span {
    font-family: Prata;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: var(--font-color1);
  }
}

// .dropdown-item:before {
//   top: 1px;
//   right: 54px;
//   border-color: #000;
//   border-color: transparent transparent rgba(0, 0, 0, 0.15) rgba(0, 0, 0, 0.15);
//   border-left: 7px solid #000;
//   border-bottom: 7px solid #000;
//   border-right: 7px solid transparent;
//   border-top: 7px solid transparent;
//   content: "";
//   position: absolute;
//   box-sizing: border-box;
//   transform-origin: 0 0;
//   transform: rotate(135deg);
//   cursor: pointer;
//   z-index: 111;
//   border-color: transparent transparent #fff #fff;
//   border-style: solid;
//   border-width: 14px;
//   box-shadow: -2px 1px 3px rgba(0, 0, 0, 0.1);
// }

.dropdown-item:hover {
  background: var(--selection);
}

.btn-like svg path {
  fill: transparent;
  stroke: gray;
}

.btn-like.is-liked path {
  fill: red !important;
  stroke: none !important;
}

.bs_wrapper_ {
  position: absolute;
  right: 75px;
  top: 32px;
}

.que_lst {
  font-family: Prata;
  font-weight: 400;
  font-size: 20px;
  line-height: 20px;
  color: var(--font-color1);
  top: 39px;
  left: 45px;
}

.btn_item_wrapper {
  width: 145px;
  height: 35px;
  border: 1px solid transparent;
  background: var(--btt_gradient);
  background-clip: padding-box,
    border-box;
  background-origin: padding-box,
    border-box;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
  cursor: pointer;
}

.btn_item_ {
  border-radius: 10px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  font-family: Prata;
  font-weight: 400;
  font-size: 17px;
  line-height: 24px;
  color: var(--font-color1);
}

.dropdown-item.in-favourites .star_w {
  background: var(--star_dark);
}
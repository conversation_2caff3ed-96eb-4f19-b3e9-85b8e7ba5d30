import { IAuth, IAuthResponse } from "@/interfaces/auth"
import { HttpClient, HttpErrorResponse } from '@angular/common/http'
import { Injectable, inject, signal } from "@angular/core"
import { Router } from '@angular/router'
import { SsrCookieService } from 'ngx-cookie-service-ssr'
import { catchError, map, throwError } from "rxjs"
import { ModalService } from './modal-service.service'
import { ProfileService } from './profile.service'
import { ToasterService } from './toaster.service'

@Injectable({
    providedIn: 'root'
})
export class AuthService {
  cookieService = inject(SsrCookieService)
  modalService = inject(ModalService)
  http = inject(HttpClient)
  toasterService = inject(ToasterService);
  router = inject(Router)
  profileService = inject(ProfileService)
  token = signal<string>(this.cookieService.get('token'))

  get isAuth() {
    return this.token()
  }

  signIn(form: IAuth) {
    return this.http.post<IAuthResponse>('/user/signin', form).pipe(
        map(response => {
            this.token.set(response.accessToken)
            this.cookieService.set('token', this.token(), {path: '/'})
            this.cookieService.set('name', (response.spiritualName || response.name), {path: '/'})
            this.cookieService.set('name', response.avatar?.name || '', {path: '/'})
            // Убираем жестко закодированное перенаправление - пусть LoginComponent обрабатывает это
        }),
      catchError((error: HttpErrorResponse) => {
        // alert(error.error.message)
        this.toasterService.showToast('Неправильные логин или пароль', 'error', 'bottom-middle', 4000);
        // this.modalService.showModal(error.error.message);
        return throwError(() => new Error(error.message));
      })
    )
}

  signUp(form: any) {
    return this.http.post('/user/signup', form)
  }

  logout() {
    // Получаем ID текущего пользователя ДО обнуления профиля
    const currentUserId = this.profileService.profile?.id;

    this.token.set("")
    this.cookieService.delete('token', '/')
    this.cookieService.delete('name', '/')
    this.cookieService.delete('avatar', '/')
    this.profileService.name.set('');
    this.profileService.avatar.set('');
    this.profileService.profile = null;

    this.router.navigate(['/ru/signin'])
}

  resetPassword(email: string) {
    return this.http.post('/user/reset-password', {email})
  }

  changePassword(form: any) {
    return this.http.post('/user/change-password', form)
  }
}

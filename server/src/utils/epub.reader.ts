import * as cheerio from "cheerio"
import * as EPub from 'epub'
import { existsSync, mkdirSync, writeFileSync } from "fs"
import { basename, join } from "path"


export class EpubReader extends EPub {
    private path: string = null
    private imageDir: string = './upload/epub/images'

    constructor(path: string) {
        // @ts-ignore
        super(path, './upload/epub/images');
        this.path = path
        this.createImageDir()
    }

    createImageDir() {
        if (!existsSync(this.imageDir)) {
            mkdirSync(this.imageDir, { recursive: true });
        }
    }

    get() {
        return new Promise((resolve, reject) => {
            if(!existsSync(this.path)) return reject(new Error('File not found'));
            this.on('error', err => reject(err));
            this.on('end', async () => resolve(await this.getChapters()))
            this.parse()
        })
    }

    downloadImages() {
        Object.entries(this.manifest).forEach(([id, item]) => {
            if (item['media-type'] && item['media-type'].startsWith('image/')) {
                this.getFile(id, (err, data) => {
                    if (err) {
                        console.error('Ошибка при извлечении изображения:', err);
                        return;
                    }
                    // Извлекаем только имя файла (например, "cover.jpg" вместо "OEBPS/cover.jpg")
                    const fileName = basename(item.href);
                    const imagePath = join(this.imageDir, fileName);
                    writeFileSync(imagePath, data);
                });
            }
        });
    }

    private extractTitle(html: string): string {
        const match = html.match(/<h1[^>]*>(.*?)<\/h1>/i); // Ищем заголовок в <h1>
        return match ? match[1].trim() : 'Без названия'; // Возвращаем заголовок или значение по умолчанию
    }

    getChapters() {
        return new Promise((resolve, reject) => {
            const chapters = []
            this.downloadImages();
            this.flow.forEach((chapter) => {
                this.getChapter(chapter.id, (err, data) => {
                    if (err) {
                        console.error('Ошибка при чтении главы:', err);
                        return;
                    }

                    const baseUrl = process.env.BASE_URL || 'http://localhost:9015'

                    const modifiedData = data
                        .replace(
                            /<image[^>]+xlink:href="([^">]+)"/g,
                            (match, src) => {
                                const imageName = src.split('/').pop(); // Извлекаем имя файла
                                return `<image xlink:href="${baseUrl}/upload/epub/images/${imageName}"`;
                            }
                        )
                        .replace(
                            /<img[^>]+src="([^">]+)"/g,
                            (match, src) => {
                                const imageName = src.split('/').pop(); // Извлекаем имя файла
                                return `<img src="${baseUrl}/upload/epub/images/${imageName}"`;
                            }
                        );

                    const $ = cheerio.load(modifiedData);
                    const title = $('h1').text() || 'Без названия';

                    chapters.push({
                        title,
                        content: modifiedData
                    });

                    if (chapters.length === this.flow.length) {
                        resolve(chapters);
                    }
                });
            });
        })
    }

}
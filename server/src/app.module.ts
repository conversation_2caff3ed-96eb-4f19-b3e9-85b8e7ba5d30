import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { ScheduleModule } from '@nestjs/schedule'
import { ServeStaticModule } from '@nestjs/serve-static'
import { TypeOrmModule } from "@nestjs/typeorm"
import { Response } from "express"
import { FileSystemStoredFile, NestjsFormDataModule } from "nestjs-form-data"
import { join } from "path"
import { AdminModule } from "./api/admin/admin.module"
import { ClientModule } from "./api/client/client.module"
import { FileModule } from './api/file/file.module'
import { FirebaseModule } from './api/firebase/firebase.module'
import { UserModule } from './api/user/user.module'

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    ScheduleModule.forRoot(),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.POSTGRES_HOST,
      port: +process.env.POSTGRES_PORT,
      username: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      database: process.env.POSTGRES_DATABASE,
      autoLoadEntities: true,
      synchronize: true
    }),
    NestjsFormDataModule.config({
      isGlobal: true,
      storage: FileSystemStoredFile,
      fileSystemStoragePath: 'upload',
      cleanupAfterSuccessHandle: false,
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'upload'),
      serveRoot: '/upload',
      serveStaticOptions: {
        setHeaders: (res: Response) => {
          res.setHeader('Access-Control-Allow-Origin', '*');
        },
        fallthrough: false
      },
    }),
    UserModule,
    ClientModule,
    AdminModule,
    FileModule,
    FirebaseModule
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}

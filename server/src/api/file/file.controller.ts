import { Body, Controller, Delete, Param, Patch, Post, Query } from '@nestjs/common'
import { existsSync, mkdirSync, renameSync, rmSync } from 'fs'
import { FileSystemStoredFile, FormDataRequest } from "nestjs-form-data"
import * as process from "node:process"
import { basename, extname, join } from "path"
import slugify from "slugify"
import { FileService } from './file.service'

@Controller('file')
export class FileController {
  constructor(private readonly fileService: FileService) {}

  // @Post('upload')
  // @FormDataRequest()
  // async upload(
  //     @Body('file') files: FileSystemStoredFile[],
  //     @Body('name') nameArr: string[],
  //     @Body('folder') folder: string
  // ) {
  //   const promises = []
  //   for(let i in files) {
  //     promises.push(this.fileService.save(files[i], folder, nameArr[i]))
  //   }
  //   return await Promise.all(promises)
  // }

  @Post('ck/upload')
  @FormDataRequest()
  async ckImageUpload(
      @Body('upload') file: FileSystemStoredFile,
      @Body('title') title: string,
      @Body('date') date: string,
      @Body('type') type: any,
      @Body('name') name: string,
      @Body('folder') folder: string = 'firebase',
      @Body('oldUrl') oldUrl: any
  ) {
    const baseUrl = process.env.BASE_URL || 'http://localhost:9015'
    const ext = extname(file.path)
    let filename = name

    if(oldUrl){
      const uploadIndex = oldUrl.indexOf('/upload');
      const result = '.' + oldUrl.substring(uploadIndex);

      if(existsSync(result)) {
        rmSync(result, {recursive: true});
      }
    }

    const uploadPath = join(__dirname, '../../../', 'upload', folder);
    if (!existsSync(uploadPath)) {
      mkdirSync(uploadPath, { recursive: true });
    }

    let time = (new Date().getTime() / 1000).toFixed(0)
    let slug = `${time}_${slugify(decodeURIComponent(name), {lower: true})}`
    let filePath = join(uploadPath, slug);

    if(title && date && type) {
      //const dateFormat = moment(date).format('YYYY MM DD')
      //type = JSON.parse(type)[0]
      //filename = `${dateFormat} ${type.value}. ${title}${ext}`;
      //renameSync(file.path, 'upload/' + folder + '/' + filename)
    } else {
      //renameSync(file.path, filePath)
    }

    renameSync(file.path, filePath)

    filePath = await this.fileService.resizeImage(filePath)

    return {url:`${baseUrl}/upload/${folder}/${basename(filePath)}`};
  }

  @Patch('sort')
  async changeSort(@Body() items: any) {
    return await this.fileService.changeSort(items)
  }

  @Patch(':id')
  async update(@Param('id') id: number, @Body() body: any) {
    return await this.fileService.update(id, body)
  }

  @Delete(':id')
  async delete(@Param('id') id: number) {
    return await this.fileService.delete(id)
  }

  @Delete()
  async deleteByPath(
      @Query('path') path: string,
      @Query('folder') folder: string = 'firebase'
  ) {
    return await this.fileService.deleteByPath(path, folder)
  }

  @Post('upload/tmp')
  @FormDataRequest()
  async uploadTmp(
      @Body('file') files: FileSystemStoredFile[],
      @Body('name') nameArr: string[],
  ) {
    const list = []
    if(!existsSync('upload/tmp')) mkdirSync('upload/tmp');

    for(let i in files) {
      let time = (new Date().getTime() / 1000).toFixed(0);
      let slug = `${time}_${slugify(decodeURIComponent(nameArr[i]), {lower: true})}`
      let path = 'tmp/' + slug;
      renameSync(files[i].path, 'upload/' + path)
      path = await this.fileService.resizeImage('./upload/' + path)
      path = path.replace('./upload/', '')
      list.push({
        name: path,
        originalName: decodeURIComponent(nameArr[i]),
      })
    }

    return list;
  }
}

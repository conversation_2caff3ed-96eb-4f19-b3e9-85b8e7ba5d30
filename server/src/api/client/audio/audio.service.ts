import { Audio } from "@/entity/Audio"
import { AudioAuthor } from "@/entity/AudioAuthor"
import { AudioFavourite } from "@/entity/AudioFavourite"
import { AudioLike } from "@/entity/AudioLike"
import { AudioPosition } from "@/entity/AudioPosition"
import { AudioStatus } from "@/entity/AudioStatus"
import { AudioTag } from "@/entity/AudioTag"
import { AudioType } from "@/entity/AudioType"
import { User } from "@/entity/User"
import { UserAudioListened } from "@/entity/UserAudioListened"
import { VideoStatus } from "@/entity/VideoStatus"
import { HttpService } from "@nestjs/axios"
import { Injectable, NotFoundException } from '@nestjs/common'
import * as moment from "moment"
import { Brackets, In } from "typeorm"

const SIMILAR_AUDIO_LIMIT = 3;

@Injectable()
export class AudioService {
    constructor(private readonly httpService: HttpService) {}

    async getByExternalId(user: any, external_id: string, view: boolean) {
        const audio = (await Audio.findOne({
            where: {external_id},
            relations: ['likes', 'likes.user', 'favourites.user', 'tags', 'listened']
        }))

        if(!audio) {
            throw new NotFoundException('Лекция не найдена');
        }

        const listened = await UserAudioListened.find({
            where: {
                audio: {
                    id: audio.id
                }
            }
        })

        let listenedLength = listened ? listened.reduce((p, c) => p + c.count, 0) : 0;

        if (view) {
            Audio.update(audio.id, {views: ++audio.views});
        }
        
        return {
            ...audio,
            likes: audio.likes_count,
            liked: user && audio.likes.some(k => k.user.id == user.id),
            inFavourites: user && audio.favourites.some(k => k.user.id == user.id),
            text: audio.text ? await this.getDocumentFromUrl(audio.text) : null,
            textLink: audio.text,
            listened: listenedLength
        }
    }

    async getDocumentFromUrl(url: string) {
        try {
            const {data} = await this.httpService.axiosRef.get(url)
            return data;
        } catch(e) {
            return null
        }
    }

    async getAll(user: any, filters: any) {
        const itemsPerPage = 10;
        const page = parseInt(filters.page) || 1;
        const startDate = filters.startDate;
        const endDate = filters.endDate;
        let years = filters.years;
        let tags = filters.tags;

        if(typeof years === "string") {
            years = [years];
        }

        // Удаляем ненужные фильтры
        ['startDate', 'endDate', 'page', 'type', 'tags', 'years'].forEach((key) => delete filters[key]);

        const queryBuilder = Audio.createQueryBuilder('audio')
            .leftJoinAndSelect('audio.likes', 'likes')
            .leftJoinAndSelect('audio.position', 'position')
            .leftJoinAndSelect('likes.user', 'likesUser')
            //.leftJoinAndSelect('audio.favourites', 'favourites')
            //.leftJoinAndSelect('favourites.user', 'favouritesUser')
            .leftJoinAndSelect('audio.listened', 'listened')
            .leftJoinAndSelect('audio.tags', 'tags')
            .take(itemsPerPage)
            .skip((page - 1) * itemsPerPage);

        queryBuilder.orderBy(`audio.date`, 'DESC');

        // Сортировка
        const sortOrderMap = {
            viewsDesc: { field: 'views', direction: 'DESC' },
            viewsAsc: { field: 'views', direction: 'ASC' },
            dateDesc: { field: 'date', direction: 'DESC' },
            dateAsc: { field: 'date', direction: 'ASC' },
            durationDesc: { field: 'duration', direction: 'DESC' },
            durationAsc: { field: 'duration', direction: 'ASC' },
            titleDesc: { field: 'title', direction: 'DESC' },
            titleAsc: { field: 'title', direction: 'ASC' },
            likesDesc: { field: 'likes_count', direction: 'DESC' },
            likesAsc: { field: 'likes_count', direction: 'ASC' },
        };

        if (sortOrderMap[filters.sortOrder]) {
            const { field, direction } = sortOrderMap[filters.sortOrder];
            queryBuilder.orderBy(`audio.${field}`, direction);
        }


        delete filters.sortOrder;

        // Фильтры

        queryBuilder.andWhere(
            "audio.link IS NOT NULL AND audio.link != '' AND (audio.link NOT LIKE :text AND audio.link NOT LIKE :text2)",
            { text: '%https://advayta.org%', text2: '%https://www.advayta.org%' }
        );

        if (filters.comment) {
            queryBuilder.andWhere('audio.comment ILIKE :comment', { comment: `%${filters.comment}%` });
        }

        if (filters.description) {
            queryBuilder.andWhere(
                new Brackets((qb) => {
                    qb.orWhere('audio.description ILIKE :description', { description: `%${filters.description}%` });
                    qb.orWhere('audio.title ILIKE :title', { title: `%${filters.description}%` });
                    qb.orWhere('tags.name ILIKE :tagName', { tagName: `%${filters.description}%` });
                }),
            );
        }

        if (years && years.length > 0) {
            queryBuilder.andWhere('EXTRACT(YEAR FROM audio.date) IN (:...years)', { years });
        }

        if (filters.youtube == 2) {
            queryBuilder.andWhere('audio.youtube IS NOT NULL');
        }

        if(filters.format && typeof filters.format === 'string') {
            filters.format = [filters.format];
        }

        if (filters.format && filters.format.length) {
            const conditions = [];
            if (filters.format.includes('text')) {
                conditions.push('audio.text_link IS NOT NULL AND audio.text_link != \'\'');
            }
            if (filters.format.includes('video')) {
                conditions.push('audio.youtube ILIKE :text');
            }
            if(filters.format.includes('paid')) {
              conditions.push('audio.paid = TRUE');
            }
            queryBuilder.andWhere(`(${conditions.join(' AND ')})`, { text: '%http%' });
        }

        if(filters.author) {
            if(!Array.isArray(filters.author)) {
                filters.author = [filters.author];
            }
            queryBuilder.andWhere('author IN (:...author)', { author: filters.author });
        }

        if (filters.durationFrom) {
            queryBuilder.andWhere('audio.duration >= :durationFrom', { durationFrom: filters.durationFrom*60 });
        }
        if (filters.durationTo) {
            queryBuilder.andWhere('audio.duration <= :durationTo', { durationTo: filters.durationTo*60 });
        }

        if (tags) {
            if (!Array.isArray(tags)) {
                tags = [tags];
            }
            queryBuilder.andWhere('tags.id IN (:...tags)', { tags });
        }

        let [items, total] = await Promise.all([queryBuilder.getMany(), queryBuilder.getCount()]);

        if (items.length === 0 && filters.format && filters.format.length && filters.format.includes('text')) {
            const newQueryBuilder = Audio.createQueryBuilder('audio')
                .leftJoinAndSelect('audio.likes', 'likes')
                .leftJoinAndSelect('likes.user', 'likesUser')
                //.leftJoinAndSelect('audio.favourites', 'favourites')
                //.leftJoinAndSelect('favourites.user', 'favouritesUser')
                .leftJoinAndSelect('audio.tags', 'tags')
                .where('audio.text IS NOT NULL'); // Ищем только аудио с текстовыми файлами

            // Выполняем новый запрос
            items = await newQueryBuilder.getMany();
            total = items.length; // Обновляем общее количество
        }

        if (filters.format && filters.format.length && filters.format.includes('text') && filters.description) {
            const filteredItems = await Promise.all(
                items.map(async (audio) => {
                    if (audio.text) {
                        try {
                            // Получаем содержимое файла по ссылке
                            const fileContent = await fetch(audio.text).then((res) => res.text());
                            // Проверяем, содержится ли filters.description в содержимом файла
                            if (fileContent.toLowerCase().includes(filters.description.toLowerCase())) {
                                return audio; // Возвращаем аудио, если вхождение найдено
                            }
                        } catch (error) {
                            console.error(`Ошибка при чтении файла: ${audio.text}`, error);
                        }
                    }
                    return null; // Иначе возвращаем null
                }),
            );

            // Убираем null из результатов
            items = filteredItems.filter((audio) => audio !== null);
            total = items.length; // Обновляем общее количество
        }

        const audio = items.map((e: any) => ({
            ...e,
            likes: e.likes_count,
            liked: user && e.likes.some((k) => k.user.id === user.id),
            listened_count: e.listened.reduce((p, c) => p + c.count, 0),
            //inFavourites: user && e.favourites.some((k) => k.user.id === user.id),
        }));


        const [authors, types, statuses, videoStatuses] = await Promise.all([
            AudioAuthor.find(),
            AudioType.find(),
            AudioStatus.find(),
            VideoStatus.find(),
        ]);

        return {
            authors,
            types,
            statuses,
            items: audio,
            videoStatuses,
            pagination: {
                total, page, itemsPerPage,
                totalPages: Math.ceil(total / itemsPerPage)
            }
        };
    }

    async like(id: number, user) {
        const audio = await Audio.findOneBy({id})
        const liked = await AudioLike.findOne({
            where: {
                audio: {
                    id
                },
                user: {
                    id: user.id
                }
            }
        })
        if (liked) {
            audio.likes_count--;
            await audio.save()
            return await AudioLike.delete(liked.id)
        }
        audio.likes_count++;
        await audio.save()
        return await AudioLike.save({
            audio,
            user
        })
    }

    async favourite(id: number, user) {
        const audio = await Audio.findOneBy({id})
        const inFavourites = await AudioFavourite.findOne({
            where: {
                audio: {
                    id
                },
                user: {
                    id: user.id
                }
            }
        })
        if (inFavourites) return await AudioFavourite.delete(inFavourites.id)
        return await AudioFavourite.save({
            audio,
            user
        })
    }

    async savePosition(body: any) {
        const [user, audio] = await Promise.all([User.findOneBy({id: body.user_id}), Audio.findOneBy({id: body.audio_id})]);
        const audioPosition = await AudioPosition.findOne({
            where: {
                audio: {
                    id: body.audio_id
                },
                user: {
                    id: body.user_id
                }
            }
        })
        if (audioPosition) {
            return await AudioPosition.update(audioPosition.id, {time: body.time})
        }
        return await AudioPosition.save({
            user, audio, time: body.time
        })
    }

    async getSavedPosition(body: any) {
        const audioPosition = await AudioPosition.findOne({
            where: {
                audio: {
                    id: body.audio_id
                },
                user: {
                    id: body.user_id
                }
            }
        })
        if (!audioPosition) return false;
        return audioPosition.time
    }

    async download(url: string, res: any) {
        const response = await this.httpService.axiosRef.get(url, {responseType: 'stream'})
        response.data.pipe(res)
        return new Promise((resolve, reject) => {
            res.on('finish', resolve);
            res.on('error', reject);
        });
    }

    async listened(userId: number, body: any) {
        const user = await User.findOneBy({id: userId})
        const audio = await Audio.findOneBy({id: body.trackId})
        const date = moment().toDate()
        const listened = await UserAudioListened.findOne({
            where: {
                audio: {
                    id: audio.id
                },
                user: {
                    id: user.id
                }
            },
        })
        if(listened) {
            listened.count++;
            listened.date = moment().toDate()
            return await listened.save()
        }
        return await UserAudioListened.save({user, audio, date, count: 1})
    }

    async getTags() {
        return await AudioTag.find();
    }

    async getListened(userId: number) {
        const userEntity = await User.findOne({where: {id: userId}, relations: ['audioListened', 'audioListened.audio']});
        return userEntity.audioListened;
    }

    async getSimilar(key: string) {
        const current = await Audio.findOne({
            where: { external_id: key },
            relations: ['tags'],
        });

        if (!current || current.tags.length === 0) {
            return this.getRandomAudios(SIMILAR_AUDIO_LIMIT, key);
        }

        const tagIds = current.tags.map(tag => tag.id);

        const rawResults = await Audio
            .createQueryBuilder('audio')
            .leftJoin('audio.tags', 'tag')
            .where('audio.external_id != :key', { key })
            .andWhere('tag.id IN (:...tagIds)', { tagIds })
            .andWhere(
                "audio.link IS NOT NULL AND audio.link != '' AND (audio.link NOT LIKE :text AND audio.link NOT LIKE :text2)",
                { text: '%https://advayta.org%', text2: '%https://www.advayta.org%' }
            )
            .select('audio.id', 'id')
            .addSelect('audio.external_id', 'external_id')
            .addSelect('COUNT(tag.id)', 'match_count')
            .groupBy('audio.id, audio.external_id')
            .orderBy('COUNT(tag.id)', 'DESC')
            .limit(SIMILAR_AUDIO_LIMIT * 3)
            .getRawMany();

        let similarAudios: Audio[] = [];
        const similarItems = rawResults.map(row => ({ id: row.id, external_id: row.external_id }));

        if (similarItems.length > 0) {
            const uniqueItems = similarItems.filter((item, index, self) =>
                index === self.findIndex(t => t.external_id === item.external_id)
            );

            const shuffledItems = uniqueItems
                .sort(() => 0.5 - Math.random())
                .slice(0, SIMILAR_AUDIO_LIMIT);

            similarAudios = await Audio.find({
                where: { id: In(shuffledItems.map(item => item.id)) },
                relations: ['tags'],
            });
        }

        if (similarAudios.length < SIMILAR_AUDIO_LIMIT) {
            const excludeExternalIds = [
                key,
                ...similarAudios.map(audio => audio.external_id)
            ].filter((v, i, a) => a.indexOf(v) === i); // Уникальные значения

            const neededCount = SIMILAR_AUDIO_LIMIT - similarAudios.length;
            const randomAudios = await this.getRandomAudios(neededCount, excludeExternalIds);
            similarAudios = [...similarAudios, ...randomAudios];
        }

        const uniqueAudios = similarAudios.filter((audio, index, self) =>
            index === self.findIndex(t => t.external_id === audio.external_id)
        );

        return uniqueAudios.slice(0, SIMILAR_AUDIO_LIMIT);
    }

    async getRandomAudios(limit: number, excludeExternalId?: string | string[]) {
        const query = Audio.createQueryBuilder('audio')
            .leftJoinAndSelect('audio.tags', 'tags');

        query.andWhere(
            "audio.link IS NOT NULL AND audio.link != '' AND (audio.link NOT LIKE :text AND audio.link NOT LIKE :text2)",
            { text: '%https://advayta.org%', text2: '%https://www.advayta.org%' }
        );

        if (excludeExternalId) {
            if (Array.isArray(excludeExternalId)) {
                if (excludeExternalId.length > 0) {
                    query.andWhere('audio.external_id NOT IN (:...excludeExternalIds)', {
                        excludeExternalIds: excludeExternalId
                    });
                }
            } else {
                query.andWhere('audio.external_id != :excludeExternalId', {
                    excludeExternalId
                });
            }
        }

        const totalCount = await query.getCount();

        if (totalCount === 0) {
            return [];
        }

        if (totalCount <= limit) {
            return query.getMany();
        }

        const skip = Math.floor(Math.random() * (totalCount - limit));
        return query
            .skip(skip)
            .take(limit)
            .getMany();
    }
}

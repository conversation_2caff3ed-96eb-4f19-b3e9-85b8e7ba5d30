import { ContentQuote } from "@/entity/ContentQuote";
import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from "@nestjs/typeorm";
import { Content } from "../../../entity/Content";
import { ContentCategory } from "../../../entity/ContentCategory";
import { StripeService } from '../donation/payment-providers/stripe.service';
import { YookassaService } from '../donation/payment-providers/yookassa.service';
import { ContentController } from './content.controller';
import { ContentService } from './content.service';

@Module({
  imports: [TypeOrmModule.forFeature([Content, ContentCategory, ContentQuote]), HttpModule],
  controllers: [ContentController],
  providers: [ContentService, YookassaService, StripeService],
})
export class ContentModule {}

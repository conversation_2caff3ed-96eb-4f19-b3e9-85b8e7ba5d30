import { Module } from '@nestjs/common'
import { AdvertisingModule } from './advertising/advertising.module'
import { AudioModule } from './audio/audio.module'
import { BackupsModule } from './backups/backups.module'
import { ConstructorModule } from './constructor/constructor.module'
import { ContentModule } from './content/content.module'
import { ForumModule } from './forum/forum.module'
import { LibraryModule } from './library/library.module'
import { MypageModule } from './mypage/mypage.module'
import { PhotoModule } from './photo/photo.module'
import { SubscriptionModule } from './subscription/subscription.module'
import { TranslationModule } from './translation/translation.module'

@Module({
    imports: [TranslationModule, ContentModule, LibraryModule, PhotoModule, AudioModule, ConstructorModule, MypageModule, ForumModule, AdvertisingModule, BackupsModule, SubscriptionModule],
    controllers: [],
    providers: [],
})
export class AdminModule {} 

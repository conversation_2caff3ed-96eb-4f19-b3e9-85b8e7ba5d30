import { FileService } from '@/api/file/file.service'
import { FirebaseService } from '@/api/firebase/firebase.service'
import { Audio } from "@/entity/Audio"
import { AudioAuthor } from "@/entity/AudioAuthor"
import { AudioFile } from '@/entity/AudioFile'
import { AudioHandler } from "@/entity/AudioHandler"
import { AudioStatus } from "@/entity/AudioStatus"
import { AudioTag } from "@/entity/AudioTag"
import { AudioType } from "@/entity/AudioType"
import { Content } from '@/entity/Content'
import { ContentCategory } from '@/entity/ContentCategory'
import { Notification, NotificationType } from '@/entity/Notification'
import { PlaylistItem } from '@/entity/PlaylistItem'
import { VideoStatus } from "@/entity/VideoStatus"
import { HttpService } from '@nestjs/axios'
import { Injectable } from '@nestjs/common'
import Bottleneck from 'bottleneck'
import { readFileSync } from 'fs'
import { marked } from 'marked'
import * as moment from 'moment'
import { FileSystemStoredFile } from "nestjs-form-data"
import { basename } from 'path'
import slugify from 'slugify'
import { And, ILike, IsNull, Not } from 'typeorm'

@Injectable()
export class AudioService {
    private statuses = [];
    private videoStatuses = [];
    private authors = [];
    private tags = []
    private entities = {}

    constructor(
        private readonly firebaseService: FirebaseService,
        private readonly httpService: HttpService,
        private readonly fileService: FileService
    ) {}

    async getAll(filters: any) {
        const where = {}
        const sort = filters.sort;
        const page: number = filters.page || 1

        if(filters.title) {
            where['title'] = ILike(`%${filters.title}%`)
        }
        if(filters.author) {
            where['author'] = filters.author
        }

        where['link'] = And(Not(ILike(`%https://advayta.org%`)), Not(ILike(`%https://www.advayta.org%`)), Not(''))

        const params = {
            where,
            // take: 10,
            // skip: (page-1) * 10,
            relations: ['likes', 'favourites', 'listened'],
            order: {}
        }
        if(sort == 'views') {
            params.order = {
                views: 'DESC'
            }
        }
        if(sort == 'likes') {
            params.order = {
                likes: {
                    id: 'ASC'
                }
            }
        }
        if(sort == 'favourites') {
            params['order'] = {
                favourites: {
                    id: 'ASC'
                }
            }
        }

        const total = await Audio.count(params)
        let items = await Audio.find(params);

        items = items.map((e: any) => {
            return {
                ...e,
                listened: e.listened.reduce((p, c) => p + c.count, 0)
            }
        })

        return {total, items}
    }

    async importFile({path}: FileSystemStoredFile) {
        const file = JSON.parse(readFileSync(path, 'utf8'));
        return await this.import(file)
    }

    async import(file: any) {
        this.statuses = file.audioStatuses
        this.authors = file.sannyasis
        this.videoStatuses = file.videoStatuses
        this.entities = {}
        const limiter = new Bottleneck({
            maxConcurrent: 10
        })
        // const entities = {
        //     'sannyasis': AudioAuthor,
        //     'audioStatuses': AudioStatus,
        //     'videoStatuses': VideoStatus,
        //     'types': AudioType,
        //     'tags': AudioTag,
        //     'handlers': AudioHandler
        // }
        const promises = []
        const entities = [
            {
                field: 'audioStatus',
                list: 'audioStatuses',
                entity: AudioStatus
            },
            {
                field: 'author',
                list: 'sannyasis',
                entity: AudioAuthor
            },
            {
                field: 'reader',
                list: 'sannyasis',
                entity: AudioAuthor
            },
            {
                field: 'videoStatus',
                list: 'videoStatuses',
                entity: VideoStatus
            },
            {
                field: 'type',
                list: 'types',
                entity: AudioType
            },
            {
                field: 'handler',
                list: 'handlers',
                entity: AudioHandler
            },
        ]
        for(let entity of entities) {
            if(!(entity.list in file)) continue;
            for(let key in file[entity.list]) {
                promises.push(
                    limiter.schedule(
                        () => this.createEntity(entity.field, entity.entity, file[entity.list][key])
                    )
                )
            }
        }

        await Promise.all(promises)

        const audio: any = Object.values(file.lectures)

        for(let item of audio) {
            if(!item.advayta) continue
            limiter.schedule(() => this.create(item)).then((audio: any) => {
                if(audio && item.tags) this.addTags(audio, Object.keys(item.tags))
            })
        }

        return file
    }

    private mapLists(audio) {
        for(let key in audio) {
            if(!(key in this.entities)) continue;
            if(key === 'tags')  {
                audio[key] = Object.keys(audio[key])
            } else {
                audio[key] = this.entities[key].find(e => e.key === audio[key]) || '';
            }
        }
        return audio
    }

    private async create(audio) {
        audio = this.mapLists(audio);
        const validation: any = this.checkLectureValidation(audio)
        if(validation.length) return false;
        const params = {
            external_id: audio.key,
            status: audio.audioStatus?.value,
            title: audio.title,
            link: audio.advayta,
            author: audio.author?.value,
            reader: audio.reader?.value,
            comment: audio.comment,
            date: moment(audio.date).format('YYYY-MM-DD'),
            description: audio.description,
            duration: this.getDuration(audio.duration),
            youtube: audio.youtube ?? null,
            videoStatus: audio.videoStatus?.value,
            seo_title: audio.seo_title,
            seo_description: audio.seo_description,
            text: audio.text,
            text_link: audio.text_link,
        }
        const item = await Audio.findOne({
            where: {external_id: audio.key},
            relations: {
                tags: true
            }
        })
        if(item) {
            Object.assign(item, params);
            return await item.save();
        }
        const {id} = await Audio.save(params);
        return await Audio.findOne({
            where: {id},
            relations: {
                tags: true
            }
        })
    }

    private async addTags(audio: Audio, tags: any[]) {
        let audioTags = await AudioTag.find()
        let tmpTags = audioTags.filter(e => tags.map(k => k.key).includes(e.external_id))
        audio.tags = tmpTags
        return await audio.save()
    }

    private async createEntity(key, entity, item) {
        this.entities[key] = [...(this.entities[key] || []), item]
        const update = await entity.findOneBy({external_id: item.key})
        if(update) return
        await entity.save({
            external_id: item.key,
            name: item.value
        })
    }
    private getDuration(duration: string) {
        if(!duration) return null
        const arr = duration.split(':')
        return (+arr[0]) * 60 * 60 + (+arr[1]) * 60 + (+arr[2]);
    }
    public async getAuthors() {
        return await AudioAuthor.find();
    }

    public async importOne(audio: any) {
        //const validation: any = this.checkLectureValidation(audio)
        //if(validation.length) return validation;
        this.tags = await AudioTag.find()

        let audioPreview = null;
        let audioPreviewUrl = null;

        if(audio.preview) {
            const uploadIndex = audio.preview.indexOf('/upload');
            const result = audio.preview.substring(uploadIndex).replace('/upload/', '');
            audioPreview = await this.fileService.save(basename(audio.preview), 'lections/preview', result)

            const baseUrl = process.env.BASE_URL || 'http://localhost:9015'
            audioPreviewUrl = `${baseUrl}/upload/${audioPreview.name}`
        }

        if (audio.lecture_link) {
            if (audio.lecture_link.includes('supabase.co')) {
                try {
                    const textContent = await this.downloadTextFromUrl(audio.lecture_link);
                    if (textContent) {
                        const createdContent = await this.createContentFromLecture(audio, textContent, audioPreview);
                        if (createdContent) {
                            audio.lecture_link = this.generateContentUrl(createdContent);
                        }
                    }
                } catch (error) {
                    console.error('Ошибка при обработке lecture_link:', error);
                }
            } else if (audio.lecture_link.includes('/categories/')) {
                try {
                    const slug = this.extractSlugFromUrl(audio.lecture_link);
                    if (slug) {
                        await this.updateContent(slug, audio, audioPreview);
                    }
                } catch (error) {
                    console.error('Ошибка при обновлении контента:', error);
                }
            } else if (audioPreview && audio.preview && audio.preview.includes('tmp')) {
                try {
                    const slug = this.extractSlugFromUrl(audio.lecture_link);
                    if (slug) {
                        await this.updateContentPreview(slug, audioPreview);
                    }
                } catch (error) {
                    console.error('Ошибка при обновлении превью контента:', error);
                }
            }
        }

        if(audio.text) {
            audio.text = this.firebaseService.copyFileFromTemp('text', audio, 'lections/text')[0]
        }

        if(audio.advayta) {
            audio.advayta = this.firebaseService.copyFileFromTemp('advayta', audio, 'lections/audio')[0]
        }

        const params = {
            external_id: audio.key,
            status: audio.audioStatus?.value || '',
            title: audio.title,
            link: audio.advayta,
            author: audio.author?.value || '',
            reader: audio.reader?.value || '',
            comment: audio.comment,
            date: moment(audio.date).format('YYYY-MM-DD'),
            description: audio.description,
            duration: this.getDuration(audio.duration),
            youtube: audio.youtube || '',
            videoStatus: audio.videoStatus?.value || '',
            seo_title: audio.seo_title,
            seo_description: audio.seo_description,
            text: audio.text,
            text_link: audio.text_link,
            lecture_link: audio.lecture_link,
            preview: audioPreviewUrl,
            paid: audio.paid
        }

        let item = await Audio.findOne({
            where: {external_id: audio.key},
            relations: {
                tags: true
            }
        })

        if(item) {
            Object.assign(item, params);
            await this.addTags(item, audio.tags)
            return await item.save();
        }

        const result = await Audio.save(params);
        item = await Audio.findOne({
            where: {external_id: audio.key},
            relations: {
                tags: true
            }
        })
        await this.addTags(item, audio.tags)

        await Notification.save({
            type: NotificationType.CONTENT_AUDIO_LECTURE_PUBLISHED,
            title: audio.title,
            link: `/ru/audiogallery/audiolektsii/${audio.key}`
        })

        return result
    }

    async getTags() {
        return await AudioTag.find()
    }

    checkLectureValidation(audio: any) {
        const errors = []
        if(!audio) errors.push('Передан пустой объект')
        if(!audio.title) errors.push('Название не заполнено')
        if(!audio.type || !['Лекция', 'Ответы на вопросы'].includes(audio.type.value)) {
            errors.push('Тип Лекция, Ответы на вопросы')
        }
        if(!audio.audioStatus || audio.audioStatus.value !== 'Обработан') {
            errors.push('Статус аудио должен быть "Обработан"')
        }
        if(!audio.advayta) {
            errors.push('Не заполнена ссылка на mp3-файл')
        }
        return errors
    }

    async getPlaylists() {
        return await PlaylistItem.find({
            where: {
                audioFile: Not(IsNull())
            }
        })
    }

    async getLecturesAndAudio(term: string) {
      const lecturesPromise = Audio.find({
        select: ['id', 'title'],
        where: {
          title: ILike(`%${term}%`)
        },
        take: 5
      })

      const audioFilesPromise = AudioFile.find({
        select: ['id', 'title'],
        where: {
          title: ILike(`%${term}%`)
        },
        take: 5
      })

      let [lectures, audioFiles] = await Promise.all([
        lecturesPromise,
        audioFilesPromise,
      ]);

      const formattedLectures = lectures.map((e) => ({...e, type: 'audio'}))
      const formattedAudioFiles = audioFiles.map((e) => ({...e, type: 'audioFile'}))

      return { items: [...formattedLectures, ...formattedAudioFiles] }
    }

    private async downloadTextFromUrl(url: string): Promise<string | null> {
        try {
            const { data } = await this.httpService.axiosRef.get(url);
            return data;
        } catch (error) {
            console.error('Ошибка при скачивании текста по URL:', error);
            return null;
        }
    }

    private convertTextToHtml(markdownText: string): string {
        if (!markdownText) return '';

        try {
            marked.setOptions({
                breaks: true,
                gfm: true 
            });

            const html = marked.parse(markdownText);

            if (html instanceof Promise) {
                return `<p>${markdownText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
            }

            return html;
        } catch (error) {
            return `<p>${markdownText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
        }
    }

    private async createContentFromLecture(audio: any, textContent: string, preview: any = null) {
        try {
            let lecturesCategory = await ContentCategory.findOne({
                where: { title: 'Лекции' }
            });

            if (!lecturesCategory) {
                lecturesCategory = await ContentCategory.save({
                    title: 'Лекции',
                    slug: slugify('Лекции', { lower: true }),
                    active: true,
                    order: 1
                });
            }

            const contentSlug = slugify(audio.title || `lecture-${audio.key}`, { lower: true });

            let contentTags: AudioTag[] = [];
            if (audio.tags && Array.isArray(audio.tags)) {
                for (let tagData of audio.tags) {
                    let tag: AudioTag;

                    if (typeof tagData === 'object' && tagData.key) {
                        tag = await AudioTag.findOneBy({ external_id: tagData.key });
                        if (!tag) {
                            tag = await AudioTag.findOneBy({ name: tagData.value || tagData.name });
                            if (!tag && tagData.value) {
                                tag = AudioTag.create({
                                    name: tagData.value,
                                    external_id: tagData.key
                                });
                                await tag.save();
                            }
                        }
                    } else if (typeof tagData === 'string') {
                        tag = await AudioTag.findOneBy({ external_id: tagData });
                    }

                    if (tag) {
                        contentTags.push(tag);
                    }
                }
            }

            const existingContent = await Content.findOne({
                where: { slug: contentSlug },
                relations: ['category', 'preview', 'tags']
            });

            const htmlContent = this.convertTextToHtml(textContent);

            if (existingContent) {
                if (preview) {
                    existingContent.preview = preview;
                }

                if(htmlContent) {
                    existingContent.content = htmlContent;
                }

                existingContent.tags = contentTags;

                await existingContent.save();
                return existingContent;
            }

            const newContent = await Content.save({
                title: audio.title || `Лекция ${audio.key}`,
                slug: contentSlug,
                seo_title: audio.seo_title || audio.title || `Лекция ${audio.key}`,
                seo_description: audio.seo_description || audio.description || '',
                content: htmlContent,
                category: lecturesCategory,
                author: audio.author?.value || audio.author || '',
                active: false,
                paid: audio.paid || false,
                lang: 'ru',
                preview: preview,
                tags: contentTags
            });

            const contentWithCategory = await Content.findOne({
                where: { id: newContent.id },
                relations: ['category', 'tags']
            });

            return contentWithCategory;

        } catch (error) {
            console.error('Ошибка при создании контента из лекции:', error);
            return null;
        }
    }

    private generateContentUrl(content: Content) {
        const baseUrl = process.env.BASE_URL || 'http://localhost:9019'
        return `${baseUrl}/ru/categories/${content.category.id}/${content.slug}`;
    }

    private extractSlugFromUrl(url: string) {
        try {
            const urlParts = url.split('/');
            const categoriesIndex = urlParts.findIndex(part => part === 'categories');
            if (categoriesIndex !== -1 && urlParts.length > categoriesIndex + 2) {
                return urlParts[categoriesIndex + 2];
            }
            return null;
        } catch (error) {
            return null;
        }
    }

    private async updateContent(slug: string, audio: any, preview: any = null) {
        try {
            const content = await Content.findOne({
                where: { slug },
                relations: ['tags', 'preview', 'category']
            });

            if (!content) {
                return;
            }

            if (audio.title) {
                content.title = audio.title;
            }

            if (audio.seo_title) {
                content.seo_title = audio.seo_title;
            }

            if (audio.seo_description) {
                content.seo_description = audio.seo_description;
            }

            if (audio.author) {
                content.author = audio.author?.value || audio.author;
            }

            if (audio.paid !== undefined) {
                content.paid = audio.paid;
            }

            if (preview) {
                content.preview = preview;

                if (!content.preview_mobile) {
                    content.preview_mobile = await this.fileService.changeImageResolution(`./upload/${preview.name}`);
                }
            }

            let contentTags: AudioTag[] = [];
            if (audio.tags && Array.isArray(audio.tags)) {
                for (let tagData of audio.tags) {
                    let tag: AudioTag;

                    if (typeof tagData === 'object' && tagData.key) {
                        tag = await AudioTag.findOneBy({ external_id: tagData.key });
                        if (!tag) {
                            tag = await AudioTag.findOneBy({ name: tagData.value || tagData.name });
                            if (!tag && tagData.value) {
                                tag = AudioTag.create({
                                    name: tagData.value,
                                    external_id: tagData.key
                                });
                                await tag.save();
                            }
                        }
                    } else if (typeof tagData === 'string') {
                        tag = await AudioTag.findOneBy({ external_id: tagData });
                    }

                    if (tag) {
                        contentTags.push(tag);
                    }
                }
            }

            content.tags = contentTags;
            await content.save();
        } catch (error) {
            console.error('Ошибка при обновлении контента:', error);
            throw error;
        }
    }

    private async updateContentPreview(slug: string, preview: any) {
        try {
            const content = await Content.findOne({
                where: { slug },
                relations: ['preview']
            });

            if (content) {
                content.preview = preview;

                if (preview && !content.preview_mobile) {
                    content.preview_mobile = await this.fileService.changeImageResolution(`./upload/${preview.name}`);
                }

                await content.save();
            }
        } catch (error) {
            console.error('Ошибка при обновлении превью контента:', error);
            throw error;
        }
    }
}

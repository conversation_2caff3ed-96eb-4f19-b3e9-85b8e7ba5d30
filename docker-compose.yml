services:
  nginx:
    container_name: 'sanatanadharma.world-nginx'
    build:
      context: ./nginx
    depends_on:
      - server
      - client
    networks:
      - internal
    volumes:
      - ./client/public:/app/client/public:ro
      - ./client/dist:/app/client/dist:ro
      - ./server/upload:/app/server/upload:ro
      - ./server/robots.txt:/app/server/robots.txt:ro
      - ./admin/dist:/app/admin/dist:ro
      - ./.well-known:/app/.well-known:ro
      - nginx-logs:/var/log/nginx

  server:
    container_name: 'sanatanadharma.world-server'
    build:
      context: ./server
    env_file:
      - .env
    networks:
      - internal
    depends_on:
      - postgresql
    healthcheck:
      test: ["CMD", "curl", "-f", "http://server:9015/api/client/translation/ru"]
      interval: 5s
      timeout: 5s
      retries: 10
    volumes:
      - ./server/upload:/server/upload

  # Angular SSR frontend
  client:
    container_name: 'sanatanadharma.world-client'
    build:
      context: ./client
      target: ${BUILD_TARGET:-production}
    env_file:
      - .env
    depends_on:
      - server
    networks:
      - internal
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://server:9015/api/client/translation/ru"]
      interval: 5s
      timeout: 5s
      retries: 10

  postgresql:
    container_name: 'sanatanadharma.world-postgres'
    image: postgres:17
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "advayta"]
      interval: 5s
      timeout: 10s
      retries: 10
    volumes:
      - postgresql-data:/var/lib/postgresql/data
      # - ./advayta-database.gz:/docker-entrypoint-initdb.d/init.sql.gz
    networks:
      - internal

networks:
  internal:

volumes:
  postgresql-data:
  nginx-logs:
